'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { WizardChat } from '@/components/wizard/WizardChat';
import { ManualQuickStart } from '@/components/wizard/ManualQuickStart';

export default function NewScenarioPage() {
  const router = useRouter();
  const [mode, setMode] = useState<'wizard' | 'manual'>('wizard');
  
  // Check if wizard is enabled via environment variable
  const wizardEnabled = process.env.NEXT_PUBLIC_WIZARD_ENABLED === 'true';

  const handleScenarioCreated = (scenarioId: string) => {
    // Use a timeout to ensure the scenario creation is fully complete
    // before navigation, which can help avoid race conditions
    setTimeout(() => {
      // Use router.replace instead of push to avoid back button issues
      router.replace(`/scenario/${scenarioId}`);
    }, 100); // Small delay to ensure state is settled
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="mx-auto max-w-7xl px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Create New Scenario</h1>
          <p className="mt-2 text-gray-600">
            Build a financial model for your business with our AI-powered wizard or manual setup.
          </p>
        </div>

        {/* Mode Toggle */}
        <div className="mb-8 flex items-center space-x-4">
          {wizardEnabled && (
            <button
              onClick={() => setMode('wizard')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                mode === 'wizard'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
              }`}
            >
              🧙 AI Wizard
            </button>
          )}
          <button
            onClick={() => setMode('manual')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              mode === 'manual'
                ? 'bg-blue-600 text-white'
                : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
            }`}
          >
            ⚡ Manual Quick Start
          </button>
        </div>

        {/* Content */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          {mode === 'wizard' && wizardEnabled ? (
            <WizardChat onScenarioCreated={handleScenarioCreated} />
          ) : (
            <ManualQuickStart onScenarioCreated={handleScenarioCreated} />
          )}
        </div>
      </div>
    </div>
  );
}