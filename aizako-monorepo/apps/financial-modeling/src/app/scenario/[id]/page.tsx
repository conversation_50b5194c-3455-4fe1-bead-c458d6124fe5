'use client';

import { useParams } from 'next/navigation';
import { useState, useEffect } from 'react';
import { useScenario } from '@/hooks/useScenario';
import { useAuth } from '@/hooks/useAuth';
import { applyLoanConfig, type LoanFormData } from '@/lib/loan';
import { ScenarioNavigation } from '@/components/scenario/ScenarioNavigation';
import { MetricsBanner } from '@/components/metrics/MetricsBanner';
import { PowerOfOne } from '@/components/PowerOfOne';
import { LoanEditor } from '@/components/LoanEditor';
import { StatementsLayout } from '@/components/statements/StatementsLayout';

export default function ScenarioPage() {
  const params = useParams();
  const scenarioId = params.id as string;
  const { isAuthenticated, isLoading: authLoading } = useAuth();

  const { scenario, loading, error, refetch } = useScenario(scenarioId);
  const [saving, setSaving] = useState(false);

  // Prevent any potential auth-related redirects by ensuring we stay on this page
  useEffect(() => {
    // Auth state monitoring for stability
  }, [isAuthenticated, authLoading]);

  // Handle loan save
  const handleLoanSave = async (loanData: LoanFormData) => {
    if (!scenario) return;
    
    setSaving(true);
    try {
      // Apply loan changes to config
      const updatedConfig = applyLoanConfig(scenario.config, loanData);
      
      // Send PATCH request to update scenario
      const response = await fetch(`/api/scenarios/${scenarioId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          version: scenario.version,
          patch: {
            config: updatedConfig,
          },
        }),
      });
      
      if (!response.ok) {
        if (response.status === 409) {
          throw new Error('Scenario has been modified by another user. Please reload the page.');
        }
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save loan changes');
      }
      
      // Refetch scenario to get updated data
      await refetch();
      
    } catch (error) {
      console.error('Error saving loan changes:', error);
      // Re-throw to let the component handle the error display
      throw error;
    } finally {
      setSaving(false);
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="mx-auto max-w-7xl px-4 py-8">
          <div className="animate-pulse">
            {/* Navigation skeleton */}
            <div className="bg-white border-b border-gray-200 mb-6">
              <div className="h-16 bg-gray-200 rounded"></div>
            </div>
            
            {/* Metrics banner skeleton */}
            <div className="bg-white border border-gray-200 rounded-lg p-6 mb-6">
              <div className="h-6 w-48 bg-gray-200 rounded mb-4"></div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="bg-gray-100 rounded-lg p-4">
                    <div className="h-4 w-20 bg-gray-200 rounded mb-2"></div>
                    <div className="h-8 w-16 bg-gray-200 rounded mb-2"></div>
                    <div className="h-3 w-full bg-gray-200 rounded"></div>
                  </div>
                ))}
              </div>
            </div>
            
            {/* Content skeleton */}
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <div className="flex space-x-4">
                  <div className="h-10 w-32 bg-gray-200 rounded"></div>
                  <div className="h-10 w-32 bg-gray-200 rounded"></div>
                  <div className="h-10 w-32 bg-gray-200 rounded"></div>
                </div>
                <div className="h-10 w-40 bg-gray-200 rounded"></div>
              </div>
              <div className="h-96 bg-white border border-gray-200 rounded-lg"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
            <svg className="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Scenario</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // Missing scenario state
  if (!scenario) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-gray-100 mb-4">
            <svg className="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Scenario Not Found</h3>
          <p className="text-gray-600 mb-4">The requested scenario could not be found or you don&apos;t have access to it.</p>
          <button
            onClick={() => window.history.back()}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  // Main content
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <ScenarioNavigation scenario={scenario} />
      
      {/* Main Content */}
      <div className="mx-auto max-w-7xl px-4 py-8">
        {/* Metrics Banner */}
        <MetricsBanner 
          snapshot={scenario.snapshot} 
          currency={scenario.config.meta.currency}
        />
        
        {/* Power of 1 Panel */}
        <PowerOfOne 
          baseConfig={scenario.config}
          currency={scenario.config.meta.currency}
        />
        
        {/* Loan Editor */}
        <LoanEditor 
          baseConfig={scenario.config}
          currency={scenario.config.meta.currency}
          onSave={handleLoanSave}
          disabled={saving}
        />
        
        {/* Statements Layout */}
        <StatementsLayout scenario={scenario} />
      </div>
    </div>
  );
}