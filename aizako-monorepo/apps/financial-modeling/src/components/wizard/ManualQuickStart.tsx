'use client';

import { useState } from 'react';
import { Loader2, TrendingUp, DollarSign, Package } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { convertRevenueToLegacy, type RevenueDriver } from '@/lib/engine/schemas';

interface ManualQuickStartProps {
  onScenarioCreated: (scenarioId: string) => void;
}

export function ManualQuickStart({ onScenarioCreated }: ManualQuickStartProps) {
  const [formData, setFormData] = useState({
    name: '',
    businessType: 'product' as 'product' | 'service' | 'saas',
    price: '',
    volume: '',
    grossMargin: '',
    currency: 'USD'
  });
  const [isCreating, setIsCreating] = useState(false);
  const { user } = useAuth();

  const updateField = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const isFormValid = () => {
    return formData.name.trim() && 
           formData.price && 
           formData.volume && 
           formData.grossMargin &&
           parseFloat(formData.price) > 0 &&
           parseFloat(formData.volume) > 0 &&
           parseFloat(formData.grossMargin) > 0 &&
           parseFloat(formData.grossMargin) <= 100;
  };

  const createScenario = async () => {
    if (!isFormValid() || !user) return;
    
    setIsCreating(true);
    
    try {
      // Build config based on business type
      let revenueConfig: RevenueDriver;
      switch (formData.businessType) {
        case 'product':
          revenueConfig = {
            kind: 'product',
            aov: parseFloat(formData.price),
            baseOrders: parseInt(formData.volume),
            momGrowthPct: 5, // Default 5% growth
            refundsPct: 0
          };
          break;
        case 'service':
          revenueConfig = {
            kind: 'service',
            rate: parseFloat(formData.price),
            billableHours: parseFloat(formData.volume),
            utilizationPct: 75 // Default 75% utilization
          };
          break;
        case 'saas':
          revenueConfig = {
            kind: 'saas',
            startSubs: parseInt(formData.volume),
            newPerMonth: Math.max(1, Math.round(parseInt(formData.volume) * 0.1)), // 10% of starting subs
            churnPct: 3, // Default 3% churn
            arpu: parseFloat(formData.price)
          };
          break;
      }

      // Convert wizard format to legacy format expected by the engine
      const legacyRevenue = convertRevenueToLegacy(revenueConfig);

      // Helper function to calculate balanced opening balances
      const calculateBalancedOpeningBalances = (userInputs: Partial<Record<string, number | undefined>> = {}) => {
        // Default values that can be overridden by user inputs
        const defaults = {
          cash: 50000,
          ar: 0,
          inventory: 0,
          ppne_net: 0,
          ap: 0,
          debt_current: 0,
          debt_long: 0,
          retained_earnings: undefined as number | undefined, // Will be calculated
        };

        // Merge user inputs with defaults
        const balances = { ...defaults, ...userInputs };

        // Calculate total assets and liabilities
        const total_assets = (balances.cash || 0) + (balances.ar || 0) +
                           (balances.inventory || 0) + (balances.ppne_net || 0);
        const total_liabilities = (balances.ap || 0) + (balances.debt_current || 0) +
                                (balances.debt_long || 0);

        // If retained earnings not specified, calculate to balance the sheet
        if (balances.retained_earnings === undefined) {
          balances.retained_earnings = total_assets - total_liabilities;
        }

        return balances;
      };

      // Build proper configuration with automatically balanced opening balances
      // This works whether user provides partial or complete balance sheet data
      const opening_balances = calculateBalancedOpeningBalances({
        // Start with reasonable defaults - user can override these later
        cash: 50000,
        // Explicitly set retained_earnings to balance the cash
        retained_earnings: 50000,  // Assets (50k cash) = Liabilities (0) + Equity (50k retained earnings)
      });

      const fullConfig = {
        meta: {
          currency: formData.currency,
          start: new Date().toISOString().split('T')[0].replace(/-\d{2}$/, '-01'), // First day of current month
          periods: 36,
          freq: 'monthly'
        },
        opening_balances,
        drivers: {
          revenue: legacyRevenue,
          gross_margin_pct: parseFloat(formData.grossMargin),
          opex: {
            fixed: Math.max(legacyRevenue.start_run_rate * 0.4, 5000), // Conservative 40% of revenue, minimum $5k
            variable_pct_of_rev: 5
          },
          capex: { items: [], depr_years: 5 },
          wc: { dso: 30, dpo: 30, dio: 0 },
          debt: { opening: 0, rate_pct: 0, term_months: 1, amort: 'interest_only', draws: [] },
          tax: { rate_pct: 25, payments_lag_mths: 1 }
        }
      };

      const response = await fetch('/api/scenarios', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          industry: 'general',
          config: fullConfig
        })
      });

      if (!response.ok) {
        throw new Error('Failed to create scenario');
      }

      const body = await response.json();
      const scenarioId = body?.data?.id ?? body?.scenario?.id;

      if (!scenarioId) {
        throw new Error('Scenario id missing from response');
      }

      onScenarioCreated(scenarioId);
    } catch (error) {
      console.error('❌ ManualQuickStart: Error creating scenario:', error);
      // Show error message to user
    } finally {
      setIsCreating(false);

    }
  };

  const getPlaceholders = () => {
    switch (formData.businessType) {
      case 'product':
        return {
          price: 'e.g., 49',
          volume: 'e.g., 800',
          priceLabel: 'Average Order Value ($)',
          volumeLabel: 'Orders per Month'
        };
      case 'service':
        return {
          price: 'e.g., 120',
          volume: 'e.g., 200',
          priceLabel: 'Hourly Rate ($)',
          volumeLabel: 'Billable Hours per Month'
        };
      case 'saas':
        return {
          price: 'e.g., 39',
          volume: 'e.g., 100',
          priceLabel: 'Monthly Price per User ($)',
          volumeLabel: 'Starting Subscribers'
        };
      default:
        return {
          price: '',
          volume: '',
          priceLabel: 'Price',
          volumeLabel: 'Volume'
        };
    }
  };

  const placeholders = getPlaceholders();

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Manual Quick Start</h2>
        <p className="text-gray-600">
          Enter your basic business metrics to create a financial model. All fields are required.
        </p>
      </div>

      <div className="space-y-6">
        {/* Scenario Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Scenario Name
          </label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => updateField('name', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="e.g., Q1 2024 Financial Model"
            required
          />
        </div>

        {/* Business Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Business Type
          </label>
          <div className="grid grid-cols-3 gap-3">
            <button
              type="button"
              onClick={() => updateField('businessType', 'product')}
              className={`p-4 rounded-lg border-2 transition-all ${
                formData.businessType === 'product'
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <Package className="w-6 h-6 mx-auto mb-2 text-gray-600" />
              <div className="text-sm font-medium">Product</div>
              <div className="text-xs text-gray-500">Physical or digital goods</div>
            </button>
            <button
              type="button"
              onClick={() => updateField('businessType', 'service')}
              className={`p-4 rounded-lg border-2 transition-all ${
                formData.businessType === 'service'
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <TrendingUp className="w-6 h-6 mx-auto mb-2 text-gray-600" />
              <div className="text-sm font-medium">Service</div>
              <div className="text-xs text-gray-500">Consulting, freelance</div>
            </button>
            <button
              type="button"
              onClick={() => updateField('businessType', 'saas')}
              className={`p-4 rounded-lg border-2 transition-all ${
                formData.businessType === 'saas'
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <DollarSign className="w-6 h-6 mx-auto mb-2 text-gray-600" />
              <div className="text-sm font-medium">SaaS</div>
              <div className="text-xs text-gray-500">Subscription software</div>
            </button>
          </div>
        </div>

        {/* Price */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {placeholders.priceLabel}
          </label>
          <input
            type="number"
            step="0.01"
            min="0"
            value={formData.price}
            onChange={(e) => updateField('price', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder={placeholders.price}
            required
          />
        </div>

        {/* Volume */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {placeholders.volumeLabel}
          </label>
          <input
            type="number"
            min="0"
            value={formData.volume}
            onChange={(e) => updateField('volume', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder={placeholders.volume}
            required
          />
        </div>

        {/* Gross Margin */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Gross Margin (%)
          </label>
          <input
            type="number"
            step="0.1"
            min="0"
            max="100"
            value={formData.grossMargin}
            onChange={(e) => updateField('grossMargin', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="e.g., 60"
            required
          />
          <p className="text-xs text-gray-500 mt-1">
            Percentage of revenue left after direct costs (COGS)
          </p>
        </div>

        {/* Currency */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Currency
          </label>
          <select
            value={formData.currency}
            onChange={(e) => updateField('currency', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="USD">USD ($)</option>
            <option value="EUR">EUR (€)</option>
            <option value="GBP">GBP (£)</option>
            <option value="CAD">CAD (C$)</option>
          </select>
        </div>

        {/* Create Button */}
        <div className="pt-4">
          <button
            onClick={createScenario}
            disabled={!isFormValid() || isCreating}
            className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
          >
            {isCreating ? (
              <>
                <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                Creating Scenario...
              </>
            ) : (
              'Create Financial Model'
            )}
          </button>
          {!isFormValid() && (
            <p className="text-xs text-gray-500 mt-2 text-center">
              Please fill in all fields with valid values
            </p>
          )}
        </div>
      </div>
    </div>
  );
}
