import { z } from 'zod';

// Zod schemas for form validation
export const metaSchema = z.object({
  currency: z.string().min(3).max(3),
  start: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  periods: z.number().min(1).max(120),
  freq: z.enum(['monthly', 'yearly']),
});

export const openingSchema = z.object({
  cash: z.number().min(0),
  ar: z.number().min(0),
  inventory: z.number().min(0),
  ppne_net: z.number().min(0),
  ap: z.number().min(0),
  debt_current: z.number().min(0),
  debt_long: z.number().min(0),
  tax_payable: z.number(),
  retained_earnings: z.number(),
});

export const revenueSchema = z.object({
  start_run_rate: z.number().min(0.01),
  mth_growth_pct: z.number().min(-50).max(100),
});

export const opexSchema = z.object({
  fixed: z.number().min(0),
  variable_pct_of_rev: z.number().min(0).max(100),
});

export const capexItemSchema = z.object({
  month: z.number().min(1),
  amount: z.number().min(0),
});

export const capexSchema = z.object({
  items: z.array(capexItemSchema),
  depr_years: z.number().min(0.1).max(50),
});

export const wcSchema = z.object({
  dso: z.number().min(0).max(365),
  dpo: z.number().min(0).max(365),
  dio: z.number().min(0).max(365),
});

export const debtDrawSchema = z.object({
  month: z.number().min(1),
  amount: z.number().min(0),
});

export const debtSchema = z.object({
  opening: z.number().min(0),
  rate_pct: z.number().min(0).max(50),
  term_months: z.number().min(1).max(360),
  amort: z.enum(['annuity', 'interest_only', 'bullet']),
  grace_period_months: z.number().min(0).max(60).optional(),
  grace_period_type: z.enum(['interest_only', 'capitalized']).optional(),
  draws: z.array(debtDrawSchema),
}).refine((data) => {
  // Grace period must be less than term
  if (data.grace_period_months && data.grace_period_months >= data.term_months) {
    return false;
  }
  return true;
}, {
  message: "Grace period must be shorter than loan term",
  path: ["grace_period_months"],
}).refine((data) => {
  // If grace period exists, must have type
  if (data.grace_period_months && data.grace_period_months > 0 && !data.grace_period_type) {
    return false;
  }
  return true;
}, {
  message: "Grace period type is required when grace period is set",
  path: ["grace_period_type"],
}).refine((data) => {
  // Warn about bullet + capitalized combination
  if (data.amort === 'bullet' && data.grace_period_type === 'capitalized' && data.grace_period_months && data.grace_period_months > 0) {
    // This is allowed but potentially problematic - let the client-side validation handle the warning
    return true;
  }
  return true;
}, {
  message: "Bullet payment with capitalized interest may result in very large final payment",
  path: ["grace_period_type"],
});

export const taxSchema = z.object({
  rate_pct: z.number().min(0).max(60),
  payments_lag_mths: z.number().min(0).max(12),
});

export const driversSchema = z.object({
  revenue: revenueSchema,
  gross_margin_pct: z.number().min(0).max(100),
  opex: opexSchema,
  capex: capexSchema,
  wc: wcSchema,
  debt: debtSchema,
  tax: taxSchema,
});

// Helper function to validate balance sheet identity
function validateBalanceSheetIdentity(balances: Record<string, number | undefined>) {
  // If balances are empty or missing, let the engine handle defaults
  if (!balances || Object.keys(balances).length === 0) {
    return true;
  }

  // Calculate totals using provided values or 0 as default
  const cash = balances.cash ?? 0;
  const ar = balances.ar ?? 0;
  const inventory = balances.inventory ?? 0;
  const ppne_net = balances.ppne_net ?? 0;
  const ap = balances.ap ?? 0;
  const debt_current = balances.debt_current ?? 0;
  const debt_long = balances.debt_long ?? 0;
  const tax_payable = balances.tax_payable ?? 0;
  const retained_earnings = balances.retained_earnings ?? 0;

  const tax_liability = Math.max(0, tax_payable);
  const tax_receivable = Math.max(0, -tax_payable);

  const total_assets = cash + ar + inventory + ppne_net + tax_receivable;
  const total_liab_equity = ap + debt_current + debt_long + tax_liability + retained_earnings;

  // Allow small rounding differences (within $0.01)
  const residual = Math.abs(total_assets - total_liab_equity);
  return residual < 0.01;
}

export const configSchema = z.object({
  meta: metaSchema,
  opening_balances: openingSchema.partial().refine(validateBalanceSheetIdentity, {
    message: "Balance sheet must balance: Assets = Liabilities + Equity. Consider adjusting retained earnings.",
    path: ["retained_earnings"],
  }),
  drivers: driversSchema,
});

// Form data type
export type ConfigFormData = z.infer<typeof configSchema>;

// Helper function to convert form data to engine config
export function formDataToConfig(data: ConfigFormData) {
  return {
    meta: data.meta,
    opening_balances: data.opening_balances,
    drivers: data.drivers,
  };
}

// Helper function to calculate balanced opening balances
export function calculateBalancedOpenings(partialBalances: Record<string, number | undefined> = {}): Record<string, number | undefined> {
  const defaults = {
    cash: 50000,
    ar: 0,
    inventory: 0,
    ppne_net: 0,
    ap: 0,
    debt_current: 0,
    debt_long: 0,
    tax_payable: 0,
    retained_earnings: undefined,
  };

  const balances = { ...defaults, ...partialBalances };

  // Calculate required retained earnings if not provided
  if (balances.retained_earnings === undefined) {
    const tax_liability = Math.max(0, balances.tax_payable || 0);
    const tax_receivable = Math.max(0, -(balances.tax_payable || 0));
    const total_assets = balances.cash + balances.ar + balances.inventory + balances.ppne_net + tax_receivable;
    const total_liabilities = balances.ap + balances.debt_current + balances.debt_long + tax_liability;
    (balances as Record<string, number | undefined>).retained_earnings = total_assets - total_liabilities;
  }

  return balances;
}

// Helper function to convert engine config to form data
import type { Config } from '@/lib/engine/types';
export function configToFormData(config: Config): ConfigFormData {
  return {
    meta: config.meta,
    opening_balances: config.opening_balances || {},
    drivers: config.drivers,
  };
}
