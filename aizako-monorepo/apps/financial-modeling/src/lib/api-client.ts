/**
 * API Client for Next.js Routes
 * Handles all communication with local Next.js API routes
 */

export interface SignInRequest {
  email: string;
  password: string;
}

export interface SignUpRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  tenantName: string;
}

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  status: 'active' | 'suspended' | 'pending' | 'invited';
  defaultTenantId?: string;
  createdAt: string;
  lastLoginAt?: string;
}

export interface Tenant {
  id: string;
  name: string;
  plan: 'free' | 'starter' | 'pro' | 'enterprise';
  modules: {
    crm: boolean;
    flows: boolean;
    financial_modeling: boolean;
    analytics: boolean;
    reporting: boolean;
  };
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface Membership {
  id: string;
  userId: string;
  tenantId: string;
  role: 'owner' | 'admin' | 'member' | 'viewer';
  joinedAt: string;
  invitedBy?: string;
}

export interface AuthResponse {
  success: boolean;
  user: User;
  tenant?: Tenant;
  membership?: {
    role: string;
    joinedAt: string;
  };
}

export interface UserWithTenants extends User {
  memberships: Membership[];
}

export interface GetCurrentUserResponse {
  success: boolean;
  user: User;
  currentTenant?: Tenant;
  membership?: {
    role: string;
    joinedAt: string;
  };
  allTenants?: Membership[];
}

export type ApiErrorResponse = {
  error: string;
  details?: unknown;
}

class ApiClient {
  constructor() {
    // No base URL needed - using relative paths for local Next.js routes
  }

  private getHeaders(): Record<string, string> {
    return {
      'Content-Type': 'application/json',
    };
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: 'Unknown error' }));
      throw new ApiError(error.error || error.message || `HTTP ${response.status}`, response.status);
    }

    return response.json();
  }


  // Auth endpoints
  async signIn(data: SignInRequest): Promise<AuthResponse> {
    const response = await fetch('/api/auth/signin', {
      method: 'POST',
      headers: this.getHeaders(),
      credentials: 'include',
      body: JSON.stringify(data),
    });

    return this.handleResponse<AuthResponse>(response);
  }

  async signUp(data: SignUpRequest): Promise<AuthResponse> {
    const response = await fetch('/api/auth/signup', {
      method: 'POST',
      headers: this.getHeaders(),
      credentials: 'include',
      body: JSON.stringify(data),
    });

    return this.handleResponse<AuthResponse>(response);
  }

  async getCurrentUser(): Promise<GetCurrentUserResponse> {
    const response = await fetch('/api/auth/me', {
      method: 'GET',
      headers: this.getHeaders(),
      credentials: 'include',
    });

    return this.handleResponse<GetCurrentUserResponse>(response);
  }

  async signOut(): Promise<void> {
    await fetch('/api/auth/signout', {
      method: 'POST',
      headers: this.getHeaders(),
      credentials: 'include',
    });
  }

}

export class ApiError extends Error {
  constructor(message: string, public statusCode?: number) {
    super(message);
    this.name = 'ApiError';
  }
}

// Export singleton instance
export const apiClient = new ApiClient();