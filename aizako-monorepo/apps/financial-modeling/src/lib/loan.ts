import type { Config, Debt, ProjectionResult } from '@/lib/engine/types';
import { project } from '@/lib/engine/project';
import { y1Metrics } from '@/lib/power';
import { calculateDebtSchedule } from '@/lib/engine/debt';

/**
 * Loan editor form data structure
 */
export interface LoanFormData {
  ratePct: number;
  termMonths: number;
  amort: 'annuity' | 'interest_only' | 'bullet';
  gracePeriodMonths: number;
  gracePeriodType: 'interest_only' | 'capitalized';
  draws: { month: number; amount: number }[];
}

/**
 * Preview metrics for loan changes
 */
export interface LoanPreviewMetrics {
  dscr: number;
  icr: number;
  cfoY1: number;
  endCashY1: number;
}

/**
 * Comparison between base and after scenarios
 */
export interface LoanComparison {
  base: LoanPreviewMetrics;
  after: LoanPreviewMetrics;
  deltas: {
    dscr: { abs: number; pct: number };
    icr: { abs: number; pct: number };
    cfoY1: { abs: number; pct: number };
    endCashY1: { abs: number; pct: number };
  };
  paymentPreview: {
    gracePeriod: {
      monthlyPayment: number;
      months: number;
    };
    afterGrace: {
      monthlyPayment: number;
      months: number;
    };
  };
}

/**
 * Extract current loan configuration from scenario config
 */
export function extractLoanConfig(config: Config): LoanFormData {
  const debt = config.drivers.debt;
  return {
    ratePct: debt.rate_pct || 0,
    termMonths: debt.term_months || 0,
    amort: debt.amort || 'annuity',
    gracePeriodMonths: debt.grace_period_months || 0,
    gracePeriodType: debt.grace_period_type || 'interest_only',
    draws: [...(debt.draws || [])],
  };
}

/**
 * Apply loan form data to a config (returns new config)
 */
export function applyLoanConfig(baseConfig: Config, loanData: LoanFormData): Config {
  const newConfig = structuredClone(baseConfig);
  
  const newDebt: Debt = {
    opening: newConfig.drivers.debt.opening, // Keep existing principal
    rate_pct: loanData.ratePct,
    term_months: loanData.termMonths,
    amort: loanData.amort,
    grace_period_months: loanData.gracePeriodMonths,
    grace_period_type: loanData.gracePeriodType,
    draws: loanData.draws,
  };
  
  newConfig.drivers.debt = newDebt;
  return newConfig;
}

/**
 * Calculate DSCR and ICR from projection result
 */
export function calculateLoanMetrics(projectionResult: ProjectionResult, config: Config): LoanPreviewMetrics {
  const y1 = y1Metrics(projectionResult);
  
  // Calculate debt schedule to get debt service information
  const debtSchedule = calculateDebtSchedule(
    config.drivers.debt.opening,
    config.drivers.debt.rate_pct,
    config.drivers.debt.term_months,
    config.drivers.debt.amort,
    config.drivers.debt.draws,
    config.drivers.debt.grace_period_months || 0,
    config.drivers.debt.grace_period_type || 'interest_only'
  );
  
  // Calculate Y1 totals
  const firstYearSchedule = debtSchedule.slice(0, Math.min(12, debtSchedule.length));
  const totalDebtServiceY1 = firstYearSchedule.reduce((sum, row) => sum + row.total_payment, 0);
  const totalInterestY1 = firstYearSchedule.reduce((sum, row) => sum + row.interest_payment, 0);
  
  // Get EBIT from Y1 P&L
  const firstYearPnL = projectionResult.pnl.slice(0, Math.min(12, projectionResult.pnl.length));
  const ebitY1 = firstYearPnL.reduce((sum, row) => sum + (row.ebit || 0), 0);
  
  // DSCR = CFO / Total Debt Service
  // CFO already excludes debt payments (they're in financing cash flow)
  const dscr = totalDebtServiceY1 > 0 ? y1.cfoY1 / totalDebtServiceY1 : 0;
  
  // ICR = EBIT / Interest Expense
  const icr = totalInterestY1 > 0 ? ebitY1 / totalInterestY1 : 0;
  
  return {
    dscr,
    icr,
    cfoY1: y1.cfoY1,
    endCashY1: y1.endCashY1,
  };
}

/**
 * Generate loan comparison between base and modified scenarios
 */
export function compareLoanScenarios(baseConfig: Config, loanData: LoanFormData): LoanComparison {
  // Calculate base metrics
  const baseProjection = project(baseConfig);
  const baseMetrics = calculateLoanMetrics(baseProjection, baseConfig);
  
  // Calculate after metrics
  const afterConfig = applyLoanConfig(baseConfig, loanData);
  const afterProjection = project(afterConfig);
  const afterMetrics = calculateLoanMetrics(afterProjection, afterConfig);
  
  // Calculate deltas
  const deltas = {
    dscr: {
      abs: afterMetrics.dscr - baseMetrics.dscr,
      pct: baseMetrics.dscr !== 0 ? (afterMetrics.dscr - baseMetrics.dscr) / Math.abs(baseMetrics.dscr) : 0,
    },
    icr: {
      abs: afterMetrics.icr - baseMetrics.icr,
      pct: baseMetrics.icr !== 0 ? (afterMetrics.icr - baseMetrics.icr) / Math.abs(baseMetrics.icr) : 0,
    },
    cfoY1: {
      abs: afterMetrics.cfoY1 - baseMetrics.cfoY1,
      pct: baseMetrics.cfoY1 !== 0 ? (afterMetrics.cfoY1 - baseMetrics.cfoY1) / Math.abs(baseMetrics.cfoY1) : 0,
    },
    endCashY1: {
      abs: afterMetrics.endCashY1 - baseMetrics.endCashY1,
      pct: baseMetrics.endCashY1 !== 0 ? (afterMetrics.endCashY1 - baseMetrics.endCashY1) / Math.abs(baseMetrics.endCashY1) : 0,
    },
  };
  
  // Calculate payment preview
  const paymentPreview = calculatePaymentPreview(
    afterConfig.drivers.debt.opening,
    loanData.ratePct,
    loanData.termMonths,
    loanData.amort,
    loanData.gracePeriodMonths,
    loanData.gracePeriodType
  );
  
  return {
    base: baseMetrics,
    after: afterMetrics,
    deltas,
    paymentPreview,
  };
}

/**
 * Calculate payment preview showing grace period and regular payments
 */
export function calculatePaymentPreview(
  principal: number,
  ratePct: number,
  termMonths: number,
  amort: 'annuity' | 'interest_only' | 'bullet',
  gracePeriodMonths: number,
  gracePeriodType: 'interest_only' | 'capitalized'
): LoanComparison['paymentPreview'] {
  const schedule = calculateDebtSchedule(
    principal,
    ratePct,
    termMonths,
    amort,
    [], // No draws for preview
    gracePeriodMonths,
    gracePeriodType
  );
  
  if (schedule.length === 0) {
    return {
      gracePeriod: { monthlyPayment: 0, months: 0 },
      afterGrace: { monthlyPayment: 0, months: 0 },
    };
  }
  
  // Grace period payments (first N months)
  const graceSchedule = schedule.slice(0, gracePeriodMonths);
  const gracePayment = graceSchedule.length > 0 ? graceSchedule[0].total_payment : 0;
  
  // After grace payments
  const afterGraceSchedule = schedule.slice(gracePeriodMonths);
  const afterGracePayment = afterGraceSchedule.length > 0 ? afterGraceSchedule[0].total_payment : 0;
  
  return {
    gracePeriod: {
      monthlyPayment: gracePayment,
      months: gracePeriodMonths,
    },
    afterGrace: {
      monthlyPayment: afterGracePayment,
      months: termMonths - gracePeriodMonths,
    },
  };
}

/**
 * Validate loan form data
 */
export interface LoanValidationError {
  field: string;
  message: string;
}

export function validateLoanData(
  loanData: LoanFormData,
  projectionPeriods: number
): LoanValidationError[] {
  const errors: LoanValidationError[] = [];
  
  // Rate validation
  if (loanData.ratePct < 0 || loanData.ratePct > 30) {
    errors.push({
      field: 'ratePct',
      message: 'Interest rate must be between 0% and 30%',
    });
  }
  
  // Term validation
  if (loanData.termMonths < 0 || loanData.termMonths > 120) {
    errors.push({
      field: 'termMonths',
      message: 'Loan term must be between 0 and 120 months',
    });
  }
  
  // Grace period validation
  if (loanData.gracePeriodMonths < 0 || loanData.gracePeriodMonths > 60) {
    errors.push({
      field: 'gracePeriodMonths',
      message: 'Grace period must be between 0 and 60 months',
    });
  }
  
  if (loanData.gracePeriodMonths >= loanData.termMonths && loanData.termMonths > 0) {
    errors.push({
      field: 'gracePeriodMonths',
      message: 'Grace period must be shorter than loan term',
    });
  }
  
  // Draws validation
  for (let i = 0; i < loanData.draws.length; i++) {
    const draw = loanData.draws[i];
    
    if (draw.month < 1 || draw.month > projectionPeriods) {
      errors.push({
        field: `draws.${i}.month`,
        message: `Draw month must be between 1 and ${projectionPeriods}`,
      });
    }
    
    if (draw.amount < 0) {
      errors.push({
        field: `draws.${i}.amount`,
        message: 'Draw amount must be positive',
      });
    }
  }
  
  // Logical validation
  if (loanData.amort === 'bullet' && loanData.gracePeriodMonths > 0 && loanData.gracePeriodType === 'capitalized') {
    errors.push({
      field: 'gracePeriodType',
      message: 'Capitalized interest with bullet payments results in very large final payment',
    });
  }
  
  return errors;
}

/**
 * Format currency for display
 */
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
}

/**
 * Format ratio for display
 */
export function formatRatio(ratio: number): string {
  if (!isFinite(ratio)) return '—';
  return `${ratio.toFixed(2)}x`;
}

/**
 * Format percentage for display
 */
export function formatPercentage(ratio: number): string {
  if (!isFinite(ratio)) return '—';
  return `${(ratio * 100).toFixed(1)}%`;
}

/**
 * Grace period educational content
 */
export const GRACE_PERIOD_EXPLANATIONS = {
  what_is_grace: "A grace period delays when you start paying principal (the loan amount). You still owe interest during this time.",
  interest_only: "You pay only interest each month during the grace period. Principal payments start after the grace period ends.",
  capitalized: "Interest gets added to your loan balance instead of being paid. No payments during grace period, but your total debt grows.",
  dscr_explanation: "DSCR (Debt Service Coverage Ratio) = Cash Flow from Operations / (Interest + Principal Payments). Values above 1.25x are typically required by lenders.",
  icr_explanation: "ICR (Interest Coverage Ratio) = EBIT / Interest Expense. Higher values indicate better ability to service interest payments.",
};

/**
 * Get example calculation for grace period
 */
export function getGracePeriodExample(
  principal: number,
  ratePct: number,
  gracePeriodMonths: number,
  gracePeriodType: 'interest_only' | 'capitalized'
): string {
  const monthlyRate = ratePct / 100 / 12;
  const monthlyInterest = principal * monthlyRate;
  
  if (gracePeriodType === 'interest_only') {
    return `${gracePeriodMonths}-month grace period on ${formatCurrency(principal)} at ${ratePct}% = ${formatCurrency(monthlyInterest)}/month interest only, then normal payments start in month ${gracePeriodMonths + 1}.`;
  } else {
    const totalCapitalizedInterest = monthlyInterest * gracePeriodMonths;
    const newPrincipal = principal + totalCapitalizedInterest;
    return `${gracePeriodMonths}-month grace period on ${formatCurrency(principal)} at ${ratePct}% = $0 payments for ${gracePeriodMonths} months, but loan balance becomes ~${formatCurrency(newPrincipal)} when payments start.`;
  }
}