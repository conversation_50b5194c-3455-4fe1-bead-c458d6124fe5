import { useState, useEffect, useCallback } from 'react';
import { PnLRow, BSRow, CFRow, Metrics, Config } from '@/lib/engine/types';

export interface ScenarioSnapshot {
  monthly: {
    pnl: PnLRow[];
    bs: BSRow[];
    cf: CFRow[];
    checks: {
      identity_residuals: number[];
      cash_flow_checks: number[];
      max_residual: number;
      passes_validation: boolean;
    };
  };
  yearly: {
    pnl: PnLRow[];
    bs: BSRow[];
    cf: CFRow[];
    metrics: Metrics[];
  };
}

export interface ScenarioData {
  id: string;
  name: string;
  industry: string;
  config: Config;
  snapshot: ScenarioSnapshot;
  version: number;
  createdAt: string;
  updatedAt: string;
}

export interface UseScenarioResult {
  scenario: ScenarioData | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

/**
 * Custom hook to fetch and manage scenario data
 */
export function useScenario(scenarioId: string): UseScenarioResult {
  const [scenario, setScenario] = useState<ScenarioData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchScenario = useCallback(async () => {
    if (!scenarioId) {
      setError('No scenario ID provided');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/scenarios/${scenarioId}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Scenario not found');
        }
        if (response.status === 403) {
          throw new Error('Access denied to this scenario');
        }
        throw new Error(`Failed to fetch scenario: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.data) {
        throw new Error('Invalid response format');
      }

      setScenario(result.data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('Error fetching scenario:', err);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [scenarioId]);

  useEffect(() => {
    fetchScenario();
  }, [fetchScenario]);

  return {
    scenario,
    loading,
    error,
    refetch: fetchScenario,
  };
}

/**
 * Hook to fetch list of scenarios for the current tenant
 */
export interface UseScenarioListResult {
  scenarios: Array<{
    id: string;
    name: string;
    industry: string;
    updatedAt: string;
  }>;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useScenarioList(): UseScenarioListResult {
  const [scenarios, setScenarios] = useState<UseScenarioListResult['scenarios']>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchScenarios = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/scenarios', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch scenarios: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.data) {
        throw new Error('Invalid response format');
      }

      setScenarios(result.data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error fetching scenarios:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchScenarios();
  }, []);

  return {
    scenarios,
    loading,
    error,
    refetch: fetchScenarios,
  };
}